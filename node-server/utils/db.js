const mysql = require('mysql2');
const Sentry = require("@sentry/node");
const { redisClient } = require('./redis-client.js');
const crypto = require("crypto");
const { v4: uuidv4 } = require('uuid');



let pool;
let readOnlyPool;
const getPool = () => {
    if (!pool) {
        pool = mysql.createPool({
            host: process.env.DATABASE_PARAMS_HOST || 'localhost',
            port: process.env.DATABASE_PARAMS_PORT || '3306',
            database: process.env.DATABASE_PARAMS_DBNAME || 'sitefotos',
            user: process.env.DATABASE_PARAMS_USERNAME || 'root',
            password: process.env.DATABASE_PARAMS_PASSWORD || '',
            waitForConnections: true,
            connectionLimit: 30,
            queueLimit: 0,
            decimalNumbers: true,
            enableKeepAlive: true,    
            idleTimeout: 30000,
            maxIdle: 5,                
            keepAliveInitialDelay: 0
        });
    }
    

    return pool.promise();
}

const getReadOnlyPool = () => {
    if (!readOnlyPool) {
        readOnlyPool = mysql.createPool({
            host: process.env.READ_ONLY_DATABASE_PARAMS_HOST || process.env.DATABASE_PARAMS_HOST || 'localhost',
            port: process.env.READ_ONLY_DATABASE_PARAMS_PORT || process.env.DATABASE_PARAMS_PORT || '3306',
            database: process.env.READ_ONLY_DATABASE_PARAMS_DBNAME || process.env.DATABASE_PARAMS_DBNAME || 'sitefotos',
            user: process.env.READ_ONLY_DATABASE_PARAMS_USERNAME || process.env.DATABASE_PARAMS_USERNAME || 'root',
            password: process.env.READ_ONLY_DATABASE_PARAMS_PASSWORD || process.env.DATABASE_PARAMS_PASSWORD || '',
            waitForConnections: true,
            connectionLimit: 30,
            queueLimit: 0,
            decimalNumbers: true,
            enableKeepAlive: true,    
            idleTimeout: 30000,
            maxIdle: 5,                
            keepAliveInitialDelay: 0
        });
    }
    return readOnlyPool.promise();
}


const checkReplicationDelay = async () => {
    try {
        const [replicaRows] = await getReadOnlyPool().execute('SELECT ts FROM heartbeat LIMIT 1');
        const [masterRows] = await getPool().execute('SELECT ts FROM heartbeat LIMIT 1');
    
        if (replicaRows.length === 0 || masterRows.length === 0) {
            console.error('Heartbeat data is missing.');
            return;
        }
        //sanity check on master time if it is more than 1 minute old, set it to infinity
        if (new Date(masterRows[0].ts).getTime() < Date.now() - 60000) {
            console.error('Master time is more than 1 minute old.');
            await redisClient.set('replication_delay', Infinity, { "EX": 10 });
            return;
        }
        const replicaTime = new Date(replicaRows[0].ts).getTime();
        const masterTime = new Date(masterRows[0].ts).getTime();

        const delay = masterTime - replicaTime;
       
        await redisClient.set('replication_delay', delay, { "EX": 10 });
    } catch (err) {
        console.error('Error checking replication delay:', err);
    }
};



const getDirectConnection = async () => {
    return mysql.createConnection({
        host: process.env.DATABASE_PARAMS_HOST || 'localhost',
        port: process.env.DATABASE_PARAMS_PORT || '3306',
        database: process.env.DATABASE_PARAMS_DBNAME || 'sitefotos',
        user: process.env.DATABASE_PARAMS_USERNAME || 'root',
        password: process.env.DATABASE_PARAMS_PASSWORD || '',
    }).promise();
}

exports.getDirectConnection = getDirectConnection;

    
// Add periodic pool monitoring
setInterval(() => {
    console.log(pool)
  if (pool) {
    console.log('Pool stats:', {
      totalConnections: pool.pool._allConnections.length,
      freeConnections: pool.pool._freeConnections.length,
      acquiringConnections: pool.pool._acquiringConnections.length
    });
  }
}, 3000);

if (process.env.K8S_BRANCH && process.env.K8S_DEPLOYMENT && process.env.K8S_BRANCH === "master" && process.env.K8S_DEPLOYMENT === "apiv1" && process.env.READ_ONLY_DATABASE_PARAMS_HOST) {
    setInterval(checkReplicationDelay, 1000);

}





   

 


exports.getConnection = async () => await getPool().getConnection();

exports.closePool = () => {
    if (pool) {
        pool.end();
        pool = undefined;
    }
    if (readOnlyPool) {
        readOnlyPool.end();
        readOnlyPool = undefined;
    }
}

// Add pool monitoring function
exports.getPoolStats = () => {
    const stats = {};

    if (pool) {
        // Get the actual pool from the promise wrapper
        const actualPool = pool.pool || pool;
        stats.mainPool = {
            totalConnections: actualPool._allConnections ? actualPool._allConnections.length : 0,
            freeConnections: actualPool._freeConnections ? actualPool._freeConnections.length : 0,
            acquiringConnections: actualPool._connectionQueue ? actualPool._connectionQueue.length : 0,
            connectionLimit: actualPool.config ? actualPool.config.connectionLimit : 'unknown',
            closed: actualPool._closed || false
        };
    } else {
        stats.mainPool = { status: 'not_initialized' };
    }

    if (readOnlyPool) {
        // Get the actual pool from the promise wrapper
        const actualReadOnlyPool = readOnlyPool.pool || readOnlyPool;
        stats.readOnlyPool = {
            totalConnections: actualReadOnlyPool._allConnections ? actualReadOnlyPool._allConnections.length : 0,
            freeConnections: actualReadOnlyPool._freeConnections ? actualReadOnlyPool._freeConnections.length : 0,
            acquiringConnections: actualReadOnlyPool._connectionQueue ? actualReadOnlyPool._connectionQueue.length : 0,
            connectionLimit: actualReadOnlyPool.config ? actualReadOnlyPool.config.connectionLimit : 'unknown',
            closed: actualReadOnlyPool._closed || false
        };
    } else {
        stats.readOnlyPool = { status: 'not_initialized' };
    }

    return stats;
}

// Start pool monitoring (always enabled for debugging)
setInterval(() => {
    const stats = exports.getPoolStats();
    console.log('MySQL Pool Stats:', JSON.stringify(stats, null, 2));
}, 30000); // Log every 30 seconds

exports.awaitSafeQuery = async (query, params, options = {}) => {
        let {
            useMainPool = false,
            acceptableDelay = 500,
            poolToUse = null
        } = options;
        let isSelectQuery = query.trim().toLowerCase().startsWith('select');
       
        let replicationDelay;
        try {
            replicationDelay = parseInt(await redisClient.get('replication_delay') || Infinity);    
        } catch (e) {
            replicationDelay = Infinity;
        }
        if (!poolToUse) {
            if (isSelectQuery && useMainPool==false && replicationDelay <= acceptableDelay) {
                poolToUse = getReadOnlyPool();
            } else {
                poolToUse = getPool();
            }
        }
        let [rows, fields] = await poolToUse.execute(query, params);
        return rows;
}

exports.awaitCachedQuery = async (query, params,  options = {}) => {
    const {
        cacheTimeOut = 120,
    } = options;
    const hash = crypto.createHash("sha1");
    hash.update(query + JSON.stringify(params));
    const cacheKey = hash.digest("hex");
    const cachedResult = await redisClient.get(cacheKey);
    if (cachedResult) {
        return JSON.parse(cachedResult);
    } else {
        const result = await this.awaitQuery(query, params);
        await redisClient.set(cacheKey, JSON.stringify(result), { "EX": cacheTimeOut });
        return result;
    }
}

exports.isValidQuery = async (query, values) => {
   
    try {   
        let p = await getPool().format(query, values);
        console.log(p)
        return true;
    } catch (e) {
        console.log(e)
        return false;
    }
}

exports.awaitQuery = async (query, params, options = {}) => {
    let {
        useMainPool = false,
        acceptableDelay = 500,
        poolToUse = null
    } = options;
    let isSelectQuery = query.trim().toLowerCase().startsWith('select');
    let replicationDelay;
    try {
        replicationDelay = parseInt(await redisClient.get('replication_delay') || Infinity);    
    } catch (e) {
        replicationDelay = Infinity;
    }   
    if (!poolToUse) {   
        if (isSelectQuery && useMainPool==false && replicationDelay <= acceptableDelay) {
            poolToUse = getReadOnlyPool();
        } else {
            poolToUse = getPool();
        }
    }
   
    let [rows, fields] = await poolToUse.query(query, params);
   
    return rows;
}
exports.awaitQuerySwitch = async (query, params, options = {}) => {
    const {
        useMainPool = false,
    } = options;
    let connectionToUse;
    try {
        let isSelectQuery = query.trim().toLowerCase().startsWith('select');



        connectionToUse = await getDirectConnection();


        let [rows, fields] = await connectionToUse.query(query, params);

        return rows;
    } catch (e) {

        throw e;
    } finally {
        if (connectionToUse) connectionToUse.end();
    }
}
//This is used to test if the query works as prepared query and as normal query
exports.awaitQueryTest = async (query, params) => {
  
        let  [rows, fields] = await getPool().query(query, params);
        let [rows2, fields2] = await getPool().execute(query, params);
        
        if (rows.length === rows2.length) {
            // Check if the file exists
            if (!fs.existsSync('queries.txt')) {
                // Create the file if it doesn't exist
                fs.writeFileSync('queries.txt', '');
            }
            
            // Check if the query already exists in the file
            let fileData = fs.readFileSync('queries.txt', 'utf-8');
            if (!fileData.includes(query)) {
                // Write the query to the file
                fs.appendFileSync('queries.txt', `${query}\n\n`);
            }
        }
        
        return rows;
   
}
exports.deleteObj = async (tableName, whereCols, whereVals) => {
    let connection;
    try {
        if (whereCols.length === 0 || whereVals.length === 0) {
            throw new Error("Where clause is required for deletes!");
        }

        connection = await getDirectConnection();
        const whereClause = whereCols.map(key => `\`${key}\` = ?`).join(' AND ');
        const sql = `DELETE FROM \`${tableName}\` WHERE ${whereClause}`;
        let [rows, fields] = await connection.execute(sql, whereVals);
        return rows;
    } catch (e) {
        throw e;
    } finally {
        if (connection) connection.end();
    }
}

exports.insertObj = async (tableName, obj, options = {}) => {
    let connection;
    let sql;
    const { poolToUse } = options;
    try {
        connection = poolToUse ? poolToUse : await getDirectConnection();
        const keys = Object.keys(obj).filter(key => !key.startsWith('_'));
        const colNames = keys.map(key => `\`${key}\``).join(', ');
        const valClause = keys.map(() => '?').join(', ');
        const values = keys.map(key => obj[key]);
        sql = `insert into ${tableName} (${colNames}) values (${valClause})`;
        const [rows] = await connection.execute(sql, values);
        return rows;
    } catch (e) {
        Sentry.captureException(e, {
            extra: {
                tableName,
                obj,
                sql
            }
        });
        throw e;
    } finally {
        if (!poolToUse && connection) {
            connection.end();
        }
    }
};


exports.updateObj = async (tableName, obj, whereCols, whereVals, options = {}) => {
    if (whereCols.length === 0 || whereVals.length === 0) {
        throw new Error("Where clause is required for updates!");
    }

    let { poolToUse, maxRetries = 5, initialDelay = 100 } = options;

    // Filter out keys that start with '_'
    const keys = Object.keys(obj).filter(key => !key.startsWith('_'));
    const updateClause = keys.map(key => `\`${key}\` = ?`).join(', ');
    let whereClause = '';
    let values = keys.map(key => obj[key]);

    whereCols.forEach((key, index) => {
        if (Array.isArray(whereVals[index])) {
            // Handle `IN` clause for arrays
            const placeholders = whereVals[index].map(() => '?').join(', ');
            whereClause += `\`${key}\` IN (${placeholders})`;
            values = values.concat(whereVals[index]);
        } else {
            // Handle simple equality conditions
            whereClause += `\`${key}\` = ?`;
            values.push(whereVals[index]);
        }

        if (index < whereCols.length - 1) {
            whereClause += ' AND ';
        }
    });

    const sql = `UPDATE \`${tableName}\` SET ${updateClause} WHERE ${whereClause}`;
    let attempt = 0;
    let delay = initialDelay;

    while (attempt < maxRetries) {
        let connection;
        

        try {
            connection = poolToUse ? poolToUse : await getDirectConnection();
            const [rows, fields] = await connection.execute(sql, values);
            if(!poolToUse && connection) {
                connection.end();
            }
            return rows;
        } catch (err) {
            if(!poolToUse && connection) {
                try {
                    connection.end();
                } catch (e) {
                    console.error(e);
                }
            }
            if (err && (err?.code === 'ER_LOCK_WAIT_TIMEOUT' || err?.code === 'ER_LOCK_DEADLOCK' || err?.errno === 1205 || err?.errno === 1213)) {
                attempt++;
                if (attempt >= maxRetries) {
                   
                    

                    Sentry.captureException(err, {
                        extra: {
                            details: `Failed to execute update after ${maxRetries} attempts.`,
                            sql,
                            values
                        }
                    });
                    const logEntry = `Failed to execute update after ${maxRetries} attempts.\nQuery: ${sql}\nParams: ${JSON.stringify(values)}\nError: ${err}\n\n`;
                    console.error(logEntry)

                    throw new Error(`Update failed after ${maxRetries} attempts due to lock contention.`);
                }
                
                console.error(`Lock contention detected. Retrying update (Attempt ${attempt} of ${maxRetries}) in ${delay}ms...\nQuery: ${sql}\nParams: ${JSON.stringify(values)}`);
                
                await new Promise(resolve => setTimeout(resolve, delay));
                
                delay *= 2;
            } else {
                Sentry.captureException(err, {
                    extra: {
                        sql,
                        values
                    }
                });
                console.error(err)
                throw err;
            }
        } 
    }

    throw new Error('Maximum retry attempts reached while waiting for row lock to be released.');
};


/*

exports.updateObj = async (tableName, obj, whereCols, whereVals, options = {}) => {
    if (whereCols.length === 0 || whereVals.length === 0) {
        throw new Error("Where clause is required for updates!");
    }

    let { maxRetries = 5, initialDelay = 100 } = options;
    const pool = getPool();

    // Filter out keys that start with '_'
    const keys = Object.keys(obj).filter(key => !key.startsWith('_'));
    const updateClause = keys.map(key => `\`${key}\` = ?`).join(', ');
    let whereClause = '';
    let values = keys.map(key => obj[key]);

    whereCols.forEach((key, index) => {
        if (Array.isArray(whereVals[index])) {
            // Handle `IN` clause for arrays
            const placeholders = whereVals[index].map(() => '?').join(', ');
            whereClause += `\`${key}\` IN (${placeholders})`;
            values = values.concat(whereVals[index]);
        } else {
            // Handle simple equality conditions
            whereClause += `\`${key}\` = ?`;
            values.push(whereVals[index]);
        }

        if (index < whereCols.length - 1) {
            whereClause += ' AND ';
        }
    });

    const sql = `UPDATE \`${tableName}\` SET ${updateClause} WHERE ${whereClause}`;
    let attempt = 0;
    let delay = initialDelay;

    while (attempt < maxRetries) {
        const connection = await pool.getConnection();
        
        try {
            await connection.execute('SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED');
            const [rows, fields] = await connection.execute(sql, values);
            await connection.execute('SET TRANSACTION ISOLATION LEVEL READ COMMITTED');  
            connection.release();
            return rows;
        } catch (err) {
            try {
                await connection.execute('SET TRANSACTION ISOLATION LEVEL READ COMMITTED');  
            } catch (resetErr) {
                console.error('Error resetting isolation level:', resetErr);
            } finally {
                connection.release();
            }
            
            if (err && (err?.code === 'ER_LOCK_WAIT_TIMEOUT' || 
                       err?.code === 'ER_LOCK_DEADLOCK' || 
                       err?.errno === 1205 || 
                       err?.errno === 1213)) {
                attempt++;
                if (attempt >= maxRetries) {
                    const logEntry = `Failed to execute update after ${maxRetries} attempts.\nQuery: ${sql}\nParams: ${JSON.stringify(values)}\nError: ${err}\n\n`;
                    console.error(logEntry);
                    throw new Error(`Update failed after ${maxRetries} attempts due to lock contention.`);
                }

                console.error(`Lock contention detected. Retrying update (Attempt ${attempt} of ${maxRetries}) in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                delay *= 2;
            } else {
                console.error(err);
                throw err;
            }
        }
    }

    throw new Error('Maximum retry attempts reached while waiting for row lock to be released.');
};

*/